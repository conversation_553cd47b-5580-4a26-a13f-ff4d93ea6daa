{"name": "atma-assessment-service", "version": "1.0.0", "description": "Assessment Service for ATMA Backend - Receives and queues assessment data", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "amqplib": "^0.10.3", "joi": "^17.9.1", "express-jwt": "^8.4.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "axios": "^1.4.0", "uuid": "^9.0.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.39.0"}, "keywords": ["assessment", "queue", "rabbitmq", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}