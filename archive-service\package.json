{"name": "atma-archive-service", "version": "1.0.0", "description": "Archive Service for ATMA Backend - Stores analysis results", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "pg": "^8.10.0", "sequelize": "^6.31.0", "express-jwt": "^8.4.1", "joi": "^17.9.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "morgan": "^1.10.0", "axios": "^1.4.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.39.0", "sequelize-cli": "^6.6.0"}, "keywords": ["archive", "storage", "microservices", "express", "nodejs", "postgresql"], "author": "ATMA Team", "license": "MIT"}