version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    container_name: atma-postgres
    environment:
      POSTGRES_DB: atma_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - atma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: atma-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - atma-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Application Services
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: atma-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
    depends_on:
      - auth-service
      - archive-service
      - assessment-service
      - notification-service
    networks:
      - atma-network
    volumes:
      - ./api-gateway/logs:/app/logs

  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: atma-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_SCHEMA=auth
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - JWT_EXPIRES_IN=7d
      - BCRYPT_ROUNDS=12
      - DEFAULT_TOKEN_BALANCE=5
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./auth-service/logs:/app/logs

  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    container_name: atma-archive-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_SCHEMA=archive
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - AUTH_SERVICE_URL=http://auth-service:3001
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./archive-service/logs:/app/logs

  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    container_name: atma-assessment-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ANALYSIS_TOKEN_COST=1
    depends_on:
      rabbitmq:
        condition: service_healthy
      auth-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./assessment-service/logs:/app/logs

  analysis-worker:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker
    environment:
      - NODE_ENV=development
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - GOOGLE_AI_MODEL=gemini-1.5-pro
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - NOTIFICATION_SERVICE_KEY=internal_service_secret_key_change_in_production
      - WORKER_CONCURRENCY=3
      - MAX_RETRIES=3
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_started
      notification-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./analysis-worker/logs:/app/logs
    deploy:
      replicas: 2  # Run 2 worker instances

  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: atma-notification-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - NOTIFICATION_QUEUE=analysis_notifications
      - EXCHANGE_NAME=atma_exchange
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - WEBSOCKET_CORS_ORIGIN=http://localhost:3000,http://localhost:3001
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./notification-service/logs:/app/logs

volumes:
  postgres_data:
    driver: local
  rabbitmq_data:
    driver: local

networks:
  atma-network:
    driver: bridge
