# API Documentation

Dokumentasi lengkap API untuk ATMA Backend microservices.

## Base URLs

- **API Gateway**: `http://localhost:3000`
- **Auth Service**: `http://localhost:3001`
- **Archive Service**: `http://localhost:3002`
- **Assessment Service**: `http://localhost:3003`
- **Notification Service**: `http://localhost:3005`

## Authentication

Semua endpoint yang me<PERSON><PERSON>an autenti<PERSON>i menggunakan JWT Bearer token:

```
Authorization: Bearer <jwt_token>
```

## API Gateway Routes

### Authentication Flow

#### 1. Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5
    },
    "token": "jwt_token"
  }
}
```

#### 2. Login User
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

#### 3. Get Profile
```http
GET /auth/profile
Authorization: Bearer <token>
```

### Assessment Flow

#### 1. Submit Assessment
```http
POST /assessments/submit
Authorization: Bearer <token>
Content-Type: application/json

{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75,
    "bodilyKinesthetic": 60,
    "musical": 55,
    "interpersonal": 70,
    "intrapersonal": 65,
    "naturalistic": 50
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "tokenCost": 1,
    "remainingTokens": 4
  }
}
```

#### 2. Check Job Status
```http
GET /assessments/job/{jobId}
Authorization: Bearer <token>
```

### Results Flow

#### 1. Get All Results
```http
GET /archive/results?page=1&limit=10&status=completed
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "uuid",
        "persona_profile": [
          {
            "archetype": "The Innovator",
            "shortSummary": "A creative problem-solver...",
            "strengths": ["Creative thinking", "Problem solving"],
            "weakness": ["Impatience", "Perfectionism"],
            "careerRecommendation": [
              {
                "career": "Software Engineer",
                "reason": "Combines analytical thinking..."
              }
            ],
            "insights": ["Focus on developing patience"],
            "workEnvironment": "Dynamic, collaborative environment",
            "roleModel": ["Steve Jobs", "Elon Musk"]
          }
        ],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### 2. Get Specific Result
```http
GET /archive/results/{resultId}
Authorization: Bearer <token>
```

## WebSocket Integration

### Connection
```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3005', {
  auth: {
    token: 'jwt_token_here'
  }
});
```

### Events

#### Analysis Complete
```javascript
socket.on('analysis-complete', (data) => {
  console.log('Analysis completed:', data);
  // data structure:
  {
    "type": "analysis-complete",
    "jobId": "uuid",
    "resultId": "uuid",
    "status": "completed",
    "message": "Your personality analysis is ready!",
    "data": {
      "archetype": "The Innovator",
      "processingTime": "3.2 minutes"
    }
  }
});
```

#### Analysis Failed
```javascript
socket.on('analysis-failed', (data) => {
  console.log('Analysis failed:', data);
  // data structure:
  {
    "type": "analysis-failed",
    "jobId": "uuid",
    "status": "failed",
    "message": "Analysis failed. Please try again.",
    "error": {
      "code": "AI_SERVICE_ERROR",
      "message": "Temporary service unavailable"
    }
  }
});
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Input validation failed | 400 |
| `UNAUTHORIZED` | Invalid or missing token | 401 |
| `INSUFFICIENT_TOKENS` | Not enough tokens | 402 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `INTERNAL_ERROR` | Server error | 500 |

## Rate Limiting

- **Window**: 15 minutes
- **Max Requests**: 100 per window per IP
- **Headers**: 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## Data Validation

### Assessment Data Requirements

#### RIASEC (6 dimensions, 0-100)
- `realistic`: Interest in working with things, tools, machines
- `investigative`: Interest in research, analysis, problem-solving
- `artistic`: Interest in creative expression, aesthetics
- `social`: Interest in helping, teaching, serving others
- `enterprising`: Interest in leading, persuading, selling
- `conventional`: Interest in organizing, following procedures

#### OCEAN (5 dimensions, 0-100)
- `openness`: Openness to experience
- `conscientiousness`: Conscientiousness and organization
- `extraversion`: Extraversion and sociability
- `agreeableness`: Agreeableness and cooperation
- `neuroticism`: Emotional stability (reverse scored)

#### VIA Character Strengths (24 strengths, 0-100)
**Wisdom & Knowledge:**
- `creativity`, `curiosity`, `judgment`, `loveOfLearning`, `perspective`

**Courage:**
- `bravery`, `perseverance`, `honesty`, `zest`

**Humanity:**
- `love`, `kindness`, `socialIntelligence`

**Justice:**
- `teamwork`, `fairness`, `leadership`

**Temperance:**
- `forgiveness`, `humility`, `prudence`, `selfRegulation`

**Transcendence:**
- `appreciationOfBeauty`, `gratitude`, `hope`, `humor`, `spirituality`

#### Multiple Intelligences (8 types, 0-100)
- `linguistic`: Word smart
- `logicalMathematical`: Number smart
- `spatial`: Picture smart
- `bodilyKinesthetic`: Body smart
- `musical`: Music smart
- `interpersonal`: People smart
- `intrapersonal`: Self smart
- `naturalistic`: Nature smart

#### Cognitive Style Index (2 dimensions, 0-100)
- `analytic`: Preference for analytical thinking
- `intuitive`: Preference for intuitive thinking

## Example Integration

### Complete Assessment Flow
```javascript
// 1. Login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});
const { data: { token } } = await loginResponse.json();

// 2. Connect to WebSocket
const socket = io('http://localhost:3005', {
  auth: { token }
});

// 3. Submit assessment
const assessmentResponse = await fetch('/assessments/submit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(assessmentData)
});
const { data: { jobId } } = await assessmentResponse.json();

// 4. Listen for completion
socket.on('analysis-complete', async (data) => {
  if (data.jobId === jobId) {
    // 5. Fetch results
    const resultsResponse = await fetch(`/archive/results/${data.resultId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const results = await resultsResponse.json();
    console.log('Analysis results:', results);
  }
});
```
