{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:11:55","url":"/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"113","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:11:55","url":"/"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:11:55 +0000] \"GET / HTTP/1.1\" 200 113 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:11:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:12:09","url":"/health/live","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:12:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:12:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:12:09"}
