{"name": "atma-analysis-worker", "version": "1.0.0", "description": "Analysis Worker for ATMA Backend - Processes assessment data using AI", "main": "src/worker.js", "scripts": {"start": "node src/worker.js", "dev": "nodemon src/worker.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"amqplib": "^0.10.3", "@google/generative-ai": "^0.2.1", "axios": "^1.4.0", "dotenv": "^16.0.3", "winston": "^3.8.2"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "eslint": "^8.39.0"}, "keywords": ["worker", "ai", "analysis", "rabbitmq", "google-ai", "microservices", "nodejs"], "author": "ATMA Team", "license": "MIT"}