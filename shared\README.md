# Shared Configuration

Direktori ini berisi konfigurasi dan utilities yang digunakan bersama oleh semua microservices.

## Struktur

```
shared/
├── config/
│   ├── database.js          # Database configuration
│   ├── jwt.js              # JWT configuration
│   ├── rabbitmq.js         # RabbitMQ configuration
│   └── logger.js           # Winston logger configuration
├── middleware/
│   ├── auth.js             # JWT authentication middleware
│   ├── validation.js       # Request validation middleware
│   ├── errorHandler.js     # Global error handler
│   └── rateLimiter.js      # Rate limiting middleware
├── utils/
│   ├── response.js         # Standardized API response format
│   ├── validation.js       # Common validation schemas
│   └── constants.js        # Application constants
├── schemas/
│   ├── assessment.js       # Assessment data validation schema
│   ├── persona.js          # Persona profile validation schema
│   └── user.js            # User data validation schema
└── README.md
```

## Database Configuration

### PostgreSQL Connection
```javascript
// shared/config/database.js
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  dialect: 'postgres',
  schema: process.env.DB_SCHEMA, // 'auth' or 'archive'
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
});
```

## JWT Configuration

### Token Settings
```javascript
// shared/config/jwt.js
module.exports = {
  secret: process.env.JWT_SECRET,
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  algorithm: 'HS256',
  issuer: 'atma-backend',
  audience: 'atma-frontend'
};
```

## RabbitMQ Configuration

### Connection and Queue Setup
```javascript
// shared/config/rabbitmq.js
const amqp = require('amqplib');

const config = {
  url: process.env.RABBITMQ_URL,
  exchange: process.env.EXCHANGE_NAME || 'atma_exchange',
  queues: {
    assessment: process.env.QUEUE_NAME || 'assessment_analysis',
    notification: process.env.NOTIFICATION_QUEUE || 'analysis_notifications',
    deadLetter: 'assessment_analysis_dlq'
  },
  routingKeys: {
    analysis: 'analysis.process',
    notification: 'notification.send'
  }
};
```

## Standardized Response Format

### Success Response
```javascript
// shared/utils/response.js
const successResponse = (res, message, data = null, statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};
```

### Error Response
```javascript
const errorResponse = (res, error, statusCode = 500) => {
  return res.status(statusCode).json({
    success: false,
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || 'An unexpected error occurred'
    },
    timestamp: new Date().toISOString()
  });
};
```

## Common Validation Schemas

### Assessment Data Schema
```javascript
// shared/schemas/assessment.js
const Joi = require('joi');

const riasecSchema = Joi.object({
  realistic: Joi.number().integer().min(0).max(100).required(),
  investigative: Joi.number().integer().min(0).max(100).required(),
  artistic: Joi.number().integer().min(0).max(100).required(),
  social: Joi.number().integer().min(0).max(100).required(),
  enterprising: Joi.number().integer().min(0).max(100).required(),
  conventional: Joi.number().integer().min(0).max(100).required()
});

const oceanSchema = Joi.object({
  openness: Joi.number().integer().min(0).max(100).required(),
  conscientiousness: Joi.number().integer().min(0).max(100).required(),
  extraversion: Joi.number().integer().min(0).max(100).required(),
  agreeableness: Joi.number().integer().min(0).max(100).required(),
  neuroticism: Joi.number().integer().min(0).max(100).required()
});

const assessmentSchema = Joi.object({
  riasec: riasecSchema.required(),
  ocean: oceanSchema.required(),
  viaIs: Joi.object().required(),
  multipleIntelligences: Joi.object().required(),
  cognitiveStyleIndex: Joi.object().required()
});
```

## Authentication Middleware

### JWT Verification
```javascript
// shared/middleware/auth.js
const jwt = require('jsonwebtoken');
const { errorResponse } = require('../utils/response');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return errorResponse(res, { 
      code: 'UNAUTHORIZED', 
      message: 'Access token is required' 
    }, 401);
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return errorResponse(res, { 
        code: 'UNAUTHORIZED', 
        message: 'Invalid or expired token' 
      }, 401);
    }
    req.user = user;
    next();
  });
};
```

## Error Handler Middleware

### Global Error Handler
```javascript
// shared/middleware/errorHandler.js
const { errorResponse } = require('../utils/response');

const errorHandler = (err, req, res, next) => {
  console.error(err.stack);

  // Joi validation error
  if (err.isJoi) {
    return errorResponse(res, {
      code: 'VALIDATION_ERROR',
      message: err.details[0].message
    }, 400);
  }

  // JWT error
  if (err.name === 'JsonWebTokenError') {
    return errorResponse(res, {
      code: 'UNAUTHORIZED',
      message: 'Invalid token'
    }, 401);
  }

  // Sequelize validation error
  if (err.name === 'SequelizeValidationError') {
    return errorResponse(res, {
      code: 'VALIDATION_ERROR',
      message: err.errors[0].message
    }, 400);
  }

  // Default error
  return errorResponse(res, {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred'
  }, 500);
};
```

## Logger Configuration

### Winston Logger
```javascript
// shared/config/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: process.env.SERVICE_NAME },
  transports: [
    new winston.transports.File({ 
      filename: process.env.LOG_FILE || 'logs/app.log' 
    }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

## Constants

### Application Constants
```javascript
// shared/utils/constants.js
module.exports = {
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    ACCEPTED: 202,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500
  },
  
  ERROR_CODES: {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    UNAUTHORIZED: 'UNAUTHORIZED',
    INSUFFICIENT_TOKENS: 'INSUFFICIENT_TOKENS',
    USER_NOT_FOUND: 'USER_NOT_FOUND',
    RESULT_NOT_FOUND: 'RESULT_NOT_FOUND',
    QUEUE_ERROR: 'QUEUE_ERROR',
    AI_SERVICE_ERROR: 'AI_SERVICE_ERROR',
    DATABASE_ERROR: 'DATABASE_ERROR',
    INTERNAL_ERROR: 'INTERNAL_ERROR'
  },

  JOB_STATUS: {
    QUEUED: 'queued',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed'
  },

  ANALYSIS_STATUS: {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed'
  }
};
```

## Usage in Services

### Import Shared Utilities
```javascript
// In any service
const { successResponse, errorResponse } = require('../shared/utils/response');
const { authenticateToken } = require('../shared/middleware/auth');
const { assessmentSchema } = require('../shared/schemas/assessment');
const logger = require('../shared/config/logger');
const constants = require('../shared/utils/constants');

// Use in routes
app.get('/api/data', authenticateToken, (req, res) => {
  const data = { message: 'Hello World' };
  return successResponse(res, 'Data retrieved successfully', data);
});
```

## Environment Variables

Setiap service harus menggunakan environment variables yang konsisten:

```env
# Common variables for all services
NODE_ENV=development
LOG_LEVEL=info
JWT_SECRET=your_jwt_secret_key

# Service-specific variables
SERVICE_NAME=service-name
PORT=3000
```
