#!/usr/bin/env node

const axios = require('axios');

const API_GATEWAY_URL = process.env.API_GATEWAY_URL || 'http://localhost:3000';

async function checkHealth() {
  try {
    console.log('🔍 Checking API Gateway health...');
    
    const response = await axios.get(`${API_GATEWAY_URL}/health`, {
      timeout: 10000
    });
    
    console.log('✅ API Gateway Health Check Results:');
    console.log(`   Status: ${response.data.status}`);
    console.log(`   Response Time: ${response.data.responseTime}`);
    console.log(`   Timestamp: ${response.data.timestamp}`);
    
    console.log('\n📊 Service Status:');
    Object.entries(response.data.services).forEach(([service, status]) => {
      const icon = status.status === 'healthy' ? '✅' : '❌';
      console.log(`   ${icon} ${service}: ${status.status}`);
      if (status.responseTime) {
        console.log(`      Response Time: ${status.responseTime}`);
      }
      if (status.error) {
        console.log(`      Error: ${status.error}`);
      }
    });
    
    console.log('\n🖥️  Gateway Info:');
    console.log(`   Uptime: ${Math.floor(response.data.gateway.uptime)}s`);
    console.log(`   Memory Usage: ${Math.round(response.data.gateway.memory.heapUsed / 1024 / 1024)}MB`);
    console.log(`   Node Version: ${response.data.gateway.nodeVersion}`);
    
    if (response.data.status === 'healthy') {
      console.log('\n🎉 All systems operational!');
      process.exit(0);
    } else {
      console.log('\n⚠️  Some services are degraded');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Health check failed:');
    
    if (error.code === 'ECONNREFUSED') {
      console.error('   API Gateway is not running or not accessible');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('   Health check request timed out');
    } else {
      console.error(`   ${error.message}`);
    }
    
    process.exit(1);
  }
}

// Run health check
checkHealth();
