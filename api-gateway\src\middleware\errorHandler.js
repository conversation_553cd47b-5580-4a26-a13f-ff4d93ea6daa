const logger = require('../utils/logger');

/**
 * Error types dan status codes
 */
const ErrorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHOR<PERSON><PERSON>ATION_ERROR: 'AUTHOR<PERSON>ZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  CONFLICT: 'CONFLICT'
};

const ErrorStatusCodes = {
  [ErrorTypes.VALIDATION_ERROR]: 400,
  [ErrorTypes.BAD_REQUEST]: 400,
  [ErrorTypes.AUTHENTICATION_ERROR]: 401,
  [ErrorTypes.AUTHORIZATION_ERROR]: 403,
  [ErrorTypes.NOT_FOUND]: 404,
  [ErrorTypes.CONFLICT]: 409,
  [ErrorTypes.RATE_LIMIT_ERROR]: 429,
  [ErrorTypes.INTERNAL_ERROR]: 500,
  [ErrorTypes.SERVICE_UNAVAILABLE]: 503
};

/**
 * Custom error class
 */
class APIError extends Error {
  constructor(type, message, details = null) {
    super(message);
    this.name = 'APIError';
    this.type = type;
    this.statusCode = ErrorStatusCodes[type] || 500;
    this.details = details;
  }
}

/**
 * Error handler middleware
 */
const errorHandler = (err, req, res, next) => {
  // Skip if response already sent
  if (res.headersSent) {
    return next(err);
  }

  let error = err;
  
  // Convert known errors to APIError
  if (!(err instanceof APIError)) {
    if (err.name === 'ValidationError') {
      error = new APIError(ErrorTypes.VALIDATION_ERROR, err.message, err.details);
    } else if (err.name === 'UnauthorizedError' || err.name === 'JsonWebTokenError') {
      error = new APIError(ErrorTypes.AUTHENTICATION_ERROR, 'Invalid or expired token');
    } else if (err.name === 'TokenExpiredError') {
      error = new APIError(ErrorTypes.AUTHENTICATION_ERROR, 'Token has expired');
    } else if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
      error = new APIError(ErrorTypes.SERVICE_UNAVAILABLE, 'Service temporarily unavailable');
    } else if (err.code === 'ETIMEDOUT') {
      error = new APIError(ErrorTypes.SERVICE_UNAVAILABLE, 'Service request timeout');
    } else {
      error = new APIError(ErrorTypes.INTERNAL_ERROR, 'Internal server error');
    }
  }

  // Log error dengan detail
  const errorLog = {
    error: {
      type: error.type || 'UNKNOWN',
      message: error.message,
      stack: error.stack,
      statusCode: error.statusCode || 500
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      userEmail: req.user?.email,
      body: req.method === 'POST' ? req.body : undefined,
      query: req.query,
      params: req.params
    },
    timestamp: new Date().toISOString()
  };

  // Log berdasarkan severity
  if (error.statusCode >= 500) {
    logger.error('Server error occurred', errorLog);
  } else if (error.statusCode >= 400) {
    logger.warn('Client error occurred', errorLog);
  } else {
    logger.info('Request error occurred', errorLog);
  }

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      code: error.type || 'INTERNAL_ERROR',
      message: error.message
    },
    timestamp: new Date().toISOString()
  };

  // Add details in development mode
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = error.details;
    errorResponse.error.stack = error.stack;
    errorResponse.request = {
      method: req.method,
      url: req.originalUrl
    };
  }

  // Add request ID if available
  if (req.requestId) {
    errorResponse.requestId = req.requestId;
  }

  // Send error response
  res.status(error.statusCode || 500).json(errorResponse);
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found handler
 */
const notFoundHandler = (req, res, next) => {
  const error = new APIError(
    ErrorTypes.NOT_FOUND,
    `Route ${req.method} ${req.originalUrl} not found`
  );
  next(error);
};

/**
 * Validation error handler
 */
const validationErrorHandler = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      const validationError = new APIError(
        ErrorTypes.VALIDATION_ERROR,
        'Validation failed',
        error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      );
      return next(validationError);
    }
    next();
  };
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler,
  validationErrorHandler,
  APIError,
  ErrorTypes
};
