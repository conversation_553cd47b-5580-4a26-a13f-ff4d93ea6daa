{"name": "atma-api-gateway", "version": "1.0.0", "description": "API Gateway for ATMA Backend Microservices", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "express-http-proxy": "^1.6.3", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "express-jwt": "^8.4.1", "morgan": "^1.10.0", "winston": "^3.8.2", "cors": "^2.8.5", "dotenv": "^16.0.3"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.39.0"}, "keywords": ["api-gateway", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}