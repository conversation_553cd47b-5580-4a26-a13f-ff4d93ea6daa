{"name": "atma-api-gateway", "version": "1.0.0", "description": "API Gateway for ATMA Backend Microservices", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "health": "node scripts/health-check.js", "security": "node scripts/security-check.js"}, "dependencies": {"axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-http-proxy": "^1.6.3", "express-jwt": "^8.4.1", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "keywords": ["api-gateway", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}