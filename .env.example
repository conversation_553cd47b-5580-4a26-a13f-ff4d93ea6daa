# Global Environment Variables for ATMA Backend
# Copy this file to .env and update the values

# Environment
NODE_ENV=development

# Security Keys (CHANGE IN PRODUCTION!)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production_minimum_32_characters
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key_here_get_from_google_ai_studio

# Service Ports
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
ARCHIVE_SERVICE_PORT=3002
ASSESSMENT_SERVICE_PORT=3003
NOTIFICATION_SERVICE_PORT=3005

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging Configuration
LOG_LEVEL=info

# Token Configuration
DEFAULT_TOKEN_BALANCE=5
ANALYSIS_TOKEN_COST=1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# JWT Configuration
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Worker Configuration
WORKER_CONCURRENCY=3
MAX_RETRIES=3

# WebSocket Configuration
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000
