const express = require('express');
const proxy = require('express-http-proxy');
const { authenticateToken } = require('../middleware/auth');
const services = require('../config/services');
const logger = require('../utils/logger');

const router = express.Router();

// All archive routes require authentication
router.use(authenticateToken);

/**
 * Proxy configuration untuk archive service
 */
const archiveProxy = proxy(services.archive.url, {
  timeout: services.archive.timeout,
  proxyReqPathResolver: (req) => {
    const path = `/archive${req.url}`;
    logger.info(`Proxying archive request to: ${services.archive.url}${path}`);
    return path;
  },
  proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
    // Forward original headers and add user context
    proxyReqOpts.headers = {
      ...proxyReqOpts.headers,
      'X-Forwarded-For': srcReq.ip,
      'X-Forwarded-Proto': srcReq.protocol,
      'X-Forwarded-Host': srcReq.get('host'),
      'X-Original-URL': srcReq.originalUrl,
      'X-User-ID': srcReq.user.id,
      'X-User-Email': srcReq.user.email
    };
    return proxyReqOpts;
  },
  userResDecorator: (proxyRes, proxyResData, userReq, userRes) => {
    try {
      const data = JSON.parse(proxyResData.toString('utf8'));
      
      // Log archive access events
      if (userReq.method === 'GET' && userRes.statusCode === 200) {
        if (userReq.path === '/results') {
          logger.info('Archive results list accessed', {
            userId: userReq.user.id,
            userEmail: userReq.user.email,
            resultCount: data.data?.results?.length || 0,
            page: userReq.query.page || 1,
            ip: userReq.ip
          });
        } else if (userReq.path.startsWith('/results/')) {
          logger.info('Archive result detail accessed', {
            userId: userReq.user.id,
            userEmail: userReq.user.email,
            resultId: userReq.params.id,
            ip: userReq.ip
          });
        }
      }
      
      return JSON.stringify(data);
    } catch (error) {
      logger.error('Error parsing archive service response', {
        error: error.message,
        path: userReq.path,
        userId: userReq.user.id
      });
      return proxyResData;
    }
  },
  proxyErrorHandler: (err, res, next) => {
    logger.error('Archive service proxy error', {
      error: err.message,
      code: err.code
    });
    
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Archive service is temporarily unavailable'
      }
    });
  }
});

// Archive routes
router.get('/results', archiveProxy);
router.get('/results/:id', archiveProxy);
router.put('/results/:id', archiveProxy);
router.delete('/results/:id', archiveProxy);

// Statistics routes
router.get('/stats', archiveProxy);
router.get('/stats/summary', archiveProxy);

module.exports = router;
