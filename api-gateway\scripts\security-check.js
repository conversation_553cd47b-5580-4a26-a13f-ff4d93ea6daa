#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 Running ATMA API Gateway Security Check...\n');

let hasErrors = false;

// Function to run a command and return a promise
function runCommand(command, args, description) {
  return new Promise((resolve, reject) => {
    console.log(`🔍 ${description}...`);
    
    const process = spawn(command, args, {
      stdio: 'pipe',
      shell: true
    });
    
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} - PASSED\n`);
        resolve({ stdout, stderr, code });
      } else {
        console.log(`❌ ${description} - FAILED`);
        if (stderr) console.log(`Error: ${stderr}`);
        console.log('');
        hasErrors = true;
        resolve({ stdout, stderr, code });
      }
    });
    
    process.on('error', (error) => {
      console.log(`❌ ${description} - ERROR: ${error.message}\n`);
      hasErrors = true;
      reject(error);
    });
  });
}

// Function to check file permissions
function checkFilePermissions() {
  console.log('🔍 Checking file permissions...');
  
  const sensitiveFiles = [
    '.env',
    'package.json',
    'src/config/services.js'
  ];
  
  let allGood = true;
  
  sensitiveFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      const mode = stats.mode.toString(8);
      console.log(`   ${file}: ${mode}`);
      
      // Check if file is world-readable (basic check)
      if (file === '.env' && (stats.mode & 0o004)) {
        console.log(`   ⚠️  Warning: ${file} is world-readable`);
        allGood = false;
      }
    }
  });
  
  if (allGood) {
    console.log('✅ File permissions check - PASSED\n');
  } else {
    console.log('❌ File permissions check - WARNINGS\n');
    hasErrors = true;
  }
}

// Function to check environment variables
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');
  
  const requiredEnvVars = [
    'JWT_SECRET',
    'AUTH_SERVICE_URL',
    'ASSESSMENT_SERVICE_URL',
    'ARCHIVE_SERVICE_URL'
  ];
  
  // Load .env file if exists
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        envVars[key.trim()] = value.trim();
      }
    });
    
    let allGood = true;
    
    requiredEnvVars.forEach(varName => {
      if (!envVars[varName]) {
        console.log(`   ❌ Missing: ${varName}`);
        allGood = false;
      } else {
        console.log(`   ✅ Found: ${varName}`);
        
        // Check for weak JWT secret
        if (varName === 'JWT_SECRET' && envVars[varName].length < 32) {
          console.log(`   ⚠️  Warning: JWT_SECRET should be at least 32 characters`);
          allGood = false;
        }
        
        // Check for default values
        if (envVars[varName].includes('your_super_secret') || envVars[varName].includes('change_in_production')) {
          console.log(`   ⚠️  Warning: ${varName} appears to use default value`);
          allGood = false;
        }
      }
    });
    
    if (allGood) {
      console.log('✅ Environment variables check - PASSED\n');
    } else {
      console.log('❌ Environment variables check - WARNINGS\n');
      hasErrors = true;
    }
  } else {
    console.log('❌ .env file not found\n');
    hasErrors = true;
  }
}

// Main security check function
async function runSecurityCheck() {
  try {
    // 1. NPM Audit
    await runCommand('npm', ['audit'], 'NPM vulnerability scan');
    
    // 2. Dependency check
    await runCommand('npm', ['outdated'], 'Dependency version check');
    
    // 3. Lint check
    await runCommand('npm', ['run', 'lint'], 'Code linting');
    
    // 4. Test security
    await runCommand('npm', ['test'], 'Security tests');
    
    // 5. File permissions
    checkFilePermissions();
    
    // 6. Environment variables
    checkEnvironmentVariables();
    
    // Summary
    console.log('🔒 Security Check Summary:');
    if (hasErrors) {
      console.log('❌ Some security checks failed or have warnings');
      console.log('📝 Please review the issues above and fix them before deployment');
      process.exit(1);
    } else {
      console.log('✅ All security checks passed!');
      console.log('🎉 API Gateway is ready for secure deployment');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ Security check failed:', error.message);
    process.exit(1);
  }
}

// Run the security check
runSecurityCheck();
