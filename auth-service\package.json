{"name": "atma-auth-service", "version": "1.0.0", "description": "Authentication Service for ATMA Backend", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "pg": "^8.10.0", "sequelize": "^6.31.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "joi": "^17.9.1", "uuid": "^9.0.0", "dotenv": "^16.0.3", "cors": "^2.8.5", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.39.0", "sequelize-cli": "^6.6.0"}, "keywords": ["authentication", "jwt", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}