# API Gateway Service

Service yang bertindak sebagai single entry point untuk semua request dari frontend ke microservices backend dalam ekosistem ATMA.

## 🚀 Fungsi Utama

1. **Routing**: Meneruskan request ke service yang tepat (auth, assessment, archive)
2. **Authentication**: Validasi JWT token dan user context
3. **Rate Limiting**: Pembatasan request per user/IP dengan konfigurasi yang fleksibel
4. **Logging**: Centralized logging dengan Winston untuk monitoring dan debugging
5. **CORS**: Cross-origin resource sharing dengan konfigurasi yang aman
6. **Security**: Helmet untuk security headers dan request validation
7. **Health Checks**: Monitoring status semua microservices
8. **Error Handling**: Centralized error handling dengan response yang konsisten

## 📋 Prerequisites

- Node.js 18+ dan npm 8+
- Akses ke microservices: auth-service, assessment-service, archive-service
- Environment variables yang dikonfigurasi dengan benar

## 🛠️ Installation

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env sesuai dengan konfigurasi environment Anda
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Run Tests**
   ```bash
   npm test
   npm run test:coverage
   ```

5. **Health Check**
   ```bash
   npm run health
   ```

## Environment Variables

```env
PORT=3000
JWT_SECRET=your_jwt_secret_key
NODE_ENV=development

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## API Routes

### Authentication Routes
Diteruskan ke Auth Service (http://localhost:3001)

#### POST /auth/register
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 0,
      "created_at": "2024-01-01T00:00:00Z"
    },
    "token": "jwt_token_here"
  }
}
```

#### POST /auth/login
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5
    },
    "token": "jwt_token_here"
  }
}
```

#### GET /auth/profile
**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### Assessment Routes
Diteruskan ke Assessment Service (http://localhost:3003)

#### POST /assessments/submit
**Headers:**
```
Authorization: Bearer jwt_token_here
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75,
    "bodilyKinesthetic": 60,
    "musical": 55,
    "interpersonal": 70,
    "intrapersonal": 65,
    "naturalistic": 50
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes"
  }
}
```

### Archive Routes
Diteruskan ke Archive Service (http://localhost:3002)

#### GET /archive/results
**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "persona_profile": [...],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### GET /archive/results/:id
**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "assessment_data": {...},
    "persona_profile": [...],
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## Health Check

#### GET /health
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "auth-service": "healthy",
    "archive-service": "healthy",
    "assessment-service": "healthy",
    "notification-service": "healthy"
  }
}
```

## Error Responses

```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid or expired token"
  }
}
```

## Rate Limiting

- **Window**: 15 minutes
- **Max Requests**: 100 per window per IP
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Logging

Semua request dicatat dengan format:
- Timestamp
- Method dan URL
- User ID (jika authenticated)
- Response status
- Response time
- IP address
