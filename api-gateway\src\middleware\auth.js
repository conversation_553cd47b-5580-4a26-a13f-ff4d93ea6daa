const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Middleware untuk validasi JWT token
 * Mengekstrak user information dari token dan menambahkannya ke req.user
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    logger.warn(`Authentication failed: No token provided for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Access token is required'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Add user information to request object
    req.user = {
      id: decoded.id,
      email: decoded.email,
      tokenBalance: decoded.tokenBalance || 0
    };

    logger.info(`Authentication successful for user ${decoded.email}`, {
      userId: decoded.id,
      path: req.path,
      method: req.method
    });

    next();
  } catch (error) {
    logger.warn(`Authentication failed: Invalid token for ${req.method} ${req.path}`, {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    let errorMessage = 'Invalid token';
    if (error.name === 'TokenExpiredError') {
      errorMessage = 'Token has expired';
    } else if (error.name === 'JsonWebTokenError') {
      errorMessage = 'Invalid token format';
    }

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: errorMessage
      }
    });
  }
};

/**
 * Middleware opsional untuk autentikasi
 * Jika token ada, akan divalidasi. Jika tidak ada, request tetap dilanjutkan
 */
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = {
      id: decoded.id,
      email: decoded.email,
      tokenBalance: decoded.tokenBalance || 0
    };
  } catch (error) {
    // Log error but don't block request
    logger.warn(`Optional auth failed: ${error.message}`, {
      path: req.path,
      method: req.method,
      ip: req.ip
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  optionalAuth
};
