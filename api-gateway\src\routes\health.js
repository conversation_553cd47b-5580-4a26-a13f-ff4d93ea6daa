const express = require('express');
const axios = require('axios');
const services = require('../config/services');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Check health of a single service
 */
const checkServiceHealth = async (serviceName, serviceConfig) => {
  try {
    const response = await axios.get(`${serviceConfig.url}/health`, {
      timeout: 5000,
      headers: {
        'User-Agent': 'ATMA-API-Gateway-HealthCheck'
      }
    });
    
    return {
      status: 'healthy',
      responseTime: response.headers['x-response-time'] || 'unknown',
      statusCode: response.status
    };
  } catch (error) {
    logger.warn(`Health check failed for ${serviceName}`, {
      error: error.message,
      code: error.code,
      url: serviceConfig.url
    });
    
    return {
      status: 'unhealthy',
      error: error.message,
      code: error.code
    };
  }
};

/**
 * Main health check endpoint
 */
router.get('/', async (req, res) => {
  const startTime = Date.now();
  
  try {
    // Check all services in parallel
    const healthChecks = await Promise.allSettled([
      checkServiceHealth('auth-service', services.auth),
      checkServiceHealth('assessment-service', services.assessment),
      checkServiceHealth('archive-service', services.archive),
      checkServiceHealth('notification-service', services.notification)
    ]);
    
    const serviceStatuses = {
      'auth-service': healthChecks[0].status === 'fulfilled' ? healthChecks[0].value : { status: 'error', error: healthChecks[0].reason },
      'assessment-service': healthChecks[1].status === 'fulfilled' ? healthChecks[1].value : { status: 'error', error: healthChecks[1].reason },
      'archive-service': healthChecks[2].status === 'fulfilled' ? healthChecks[2].value : { status: 'error', error: healthChecks[2].reason },
      'notification-service': healthChecks[3].status === 'fulfilled' ? healthChecks[3].value : { status: 'error', error: healthChecks[3].reason }
    };
    
    // Determine overall health status
    const allHealthy = Object.values(serviceStatuses).every(service => service.status === 'healthy');
    const overallStatus = allHealthy ? 'healthy' : 'degraded';
    
    const responseTime = Date.now() - startTime;
    
    const healthResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      version: '1.0.0',
      services: serviceStatuses,
      gateway: {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version
      }
    };
    
    // Log health check
    logger.info('Health check performed', {
      overallStatus,
      responseTime: `${responseTime}ms`,
      servicesHealthy: Object.values(serviceStatuses).filter(s => s.status === 'healthy').length,
      totalServices: Object.keys(serviceStatuses).length
    });
    
    // Return appropriate status code
    const statusCode = overallStatus === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthResponse);
    
  } catch (error) {
    logger.error('Health check error', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: {
        code: 'HEALTH_CHECK_ERROR',
        message: 'Failed to perform health check'
      }
    });
  }
});

/**
 * Detailed health check endpoint
 */
router.get('/detailed', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const healthChecks = await Promise.allSettled([
      checkServiceHealth('auth-service', services.auth),
      checkServiceHealth('assessment-service', services.assessment),
      checkServiceHealth('archive-service', services.archive),
      checkServiceHealth('notification-service', services.notification)
    ]);
    
    const serviceStatuses = {
      'auth-service': healthChecks[0].status === 'fulfilled' ? healthChecks[0].value : { status: 'error', error: healthChecks[0].reason },
      'assessment-service': healthChecks[1].status === 'fulfilled' ? healthChecks[1].value : { status: 'error', error: healthChecks[1].reason },
      'archive-service': healthChecks[2].status === 'fulfilled' ? healthChecks[2].value : { status: 'error', error: healthChecks[2].reason },
      'notification-service': healthChecks[3].status === 'fulfilled' ? healthChecks[3].value : { status: 'error', error: healthChecks[3].reason }
    };
    
    const allHealthy = Object.values(serviceStatuses).every(service => service.status === 'healthy');
    const overallStatus = allHealthy ? 'healthy' : 'degraded';
    
    const responseTime = Date.now() - startTime;
    
    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      version: '1.0.0',
      services: serviceStatuses,
      gateway: {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV || 'development',
        pid: process.pid
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        cpuUsage: process.cpuUsage()
      }
    });
    
  } catch (error) {
    logger.error('Detailed health check error', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: {
        code: 'HEALTH_CHECK_ERROR',
        message: 'Failed to perform detailed health check'
      }
    });
  }
});

/**
 * Simple liveness probe
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString()
  });
});

/**
 * Readiness probe
 */
router.get('/ready', async (req, res) => {
  try {
    // Quick check of critical services
    const authCheck = await checkServiceHealth('auth-service', services.auth);
    
    if (authCheck.status === 'healthy') {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not-ready',
        timestamp: new Date().toISOString(),
        reason: 'Critical services unavailable'
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not-ready',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

module.exports = router;
