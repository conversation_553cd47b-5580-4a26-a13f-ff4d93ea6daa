module.exports = {
  auth: {
    url: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
    timeout: 30000
  },
  assessment: {
    url: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
    timeout: 30000
  },
  archive: {
    url: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
    timeout: 30000
  },
  notification: {
    url: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
    timeout: 30000
  }
};
