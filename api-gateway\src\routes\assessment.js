const express = require('express');
const proxy = require('express-http-proxy');
const { assessmentRateLimit } = require('../middleware/rateLimiter');
const { authenticateToken } = require('../middleware/auth');
const services = require('../config/services');
const logger = require('../utils/logger');

const router = express.Router();

// All assessment routes require authentication
router.use(authenticateToken);

// Rate limiting untuk assessment endpoints
router.use(assessmentRateLimit);

/**
 * Proxy configuration untuk assessment service
 */
const assessmentProxy = proxy(services.assessment.url, {
  timeout: services.assessment.timeout,
  proxyReqPathResolver: (req) => {
    const path = `/assessments${req.url}`;
    logger.info(`Proxying assessment request to: ${services.assessment.url}${path}`);
    return path;
  },
  proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
    // Forward original headers and add user context
    proxyReqOpts.headers = {
      ...proxyReqOpts.headers,
      'X-Forwarded-For': srcReq.ip,
      'X-Forwarded-Proto': srcReq.protocol,
      'X-Forwarded-Host': srcReq.get('host'),
      'X-Original-URL': srcReq.originalUrl,
      'X-User-ID': srcReq.user.id,
      'X-User-Email': srcReq.user.email,
      'X-User-Token-Balance': srcReq.user.tokenBalance.toString()
    };
    return proxyReqOpts;
  },
  proxyReqBodyDecorator: (bodyContent, srcReq) => {
    try {
      if (srcReq.method === 'POST' && srcReq.path === '/submit') {
        const body = JSON.parse(bodyContent.toString());
        
        // Add user context to request body
        body.userId = srcReq.user.id;
        body.userEmail = srcReq.user.email;
        
        logger.info('Assessment submission received', {
          userId: srcReq.user.id,
          userEmail: srcReq.user.email,
          assessmentTypes: Object.keys(body).filter(key => 
            ['riasec', 'ocean', 'viaIs', 'multipleIntelligences', 'cognitiveStyleIndex'].includes(key)
          ),
          ip: srcReq.ip
        });
        
        return JSON.stringify(body);
      }
      return bodyContent;
    } catch (error) {
      logger.error('Error processing assessment request body', {
        error: error.message,
        userId: srcReq.user.id
      });
      return bodyContent;
    }
  },
  userResDecorator: (proxyRes, proxyResData, userReq, userRes) => {
    try {
      const data = JSON.parse(proxyResData.toString('utf8'));
      
      // Log assessment events
      if (userReq.path === '/submit' && userRes.statusCode === 200) {
        logger.info('Assessment submission successful', {
          userId: userReq.user.id,
          userEmail: userReq.user.email,
          jobId: data.data?.jobId,
          ip: userReq.ip
        });
      }
      
      return JSON.stringify(data);
    } catch (error) {
      logger.error('Error parsing assessment service response', {
        error: error.message,
        path: userReq.path,
        userId: userReq.user.id
      });
      return proxyResData;
    }
  },
  proxyErrorHandler: (err, res, next) => {
    logger.error('Assessment service proxy error', {
      error: err.message,
      code: err.code
    });
    
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Assessment service is temporarily unavailable'
      }
    });
  }
});

// Assessment routes
router.post('/submit', assessmentProxy);
router.get('/status/:jobId', assessmentProxy);

module.exports = router;
