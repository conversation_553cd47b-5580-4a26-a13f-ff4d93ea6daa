# Server Configuration
PORT=3005
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# RabbitMQ Configuration (for listening to events)
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
NOTIFICATION_QUEUE=analysis_notifications
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=notification.send

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# WebSocket Configuration
WEBSOCKET_CORS_ORIGIN=http://localhost:3000,http://localhost:3001
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000
WEBSOCKET_MAX_CONNECTIONS=1000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/notification-service.log
