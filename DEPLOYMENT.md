# Deployment Guide

Panduan deployment untuk ATMA Backend microservices di berbagai environment.

## Environment Setup

### Development
```bash
# 1. Setup environment variables
cp .env.example .env
# Edit .env dengan konfigurasi development

# 2. Start infrastructure
npm run infrastructure:up

# 3. Install dependencies
npm run install:all

# 4. Start services
npm run dev
```

### Production

#### Using Docker Compose
```bash
# 1. Setup production environment
cp .env.example .env.production
# Edit dengan konfigurasi production

# 2. Build images
docker-compose build

# 3. Start services
docker-compose -f docker-compose.yml up -d

# 4. Monitor logs
docker-compose logs -f
```

#### Manual Deployment
```bash
# 1. Install dependencies
npm run install:all

# 2. Build applications (jika ada build step)
npm run build

# 3. Start services
NODE_ENV=production npm start
```

## Environment Variables

### Required Variables
```env
# Security (WAJIB DIGANTI DI PRODUCTION)
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters
INTERNAL_SERVICE_KEY=internal_service_secret_key

# Google AI
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=secure_password

# RabbitMQ
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=secure_password
```

### Optional Variables
```env
# Logging
LOG_LEVEL=info

# Performance
WORKER_CONCURRENCY=3
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=https://yourdomain.com
```

## Database Setup

### PostgreSQL Configuration
```sql
-- Create main database
CREATE DATABASE atma_db;

-- Create user (production)
CREATE USER atma_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to database and create schemas
\c atma_db;
CREATE SCHEMA auth;
CREATE SCHEMA archive;

-- Grant schema permissions
GRANT USAGE ON SCHEMA auth TO atma_user;
GRANT USAGE ON SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO atma_user;
```

### Migration
```bash
# Run migrations
npm run migrate:auth
npm run migrate:archive

# Or using Docker
docker-compose exec auth-service npm run migrate
docker-compose exec archive-service npm run migrate
```

## RabbitMQ Setup

### Queue Configuration
```bash
# Create exchanges and queues
rabbitmqctl add_vhost atma
rabbitmqctl set_permissions -p atma atma_user ".*" ".*" ".*"

# Enable management plugin
rabbitmq-plugins enable rabbitmq_management
```

### Queue Monitoring
- Management UI: http://localhost:15672
- Default credentials: guest/guest (change in production)

## Load Balancing

### Nginx Configuration
```nginx
upstream api_gateway {
    server localhost:3000;
}

upstream auth_service {
    server localhost:3001;
}

server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /auth/ {
        proxy_pass http://auth_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Scaling

### Horizontal Scaling
```bash
# Scale analysis workers
docker-compose up -d --scale analysis-worker=5

# Scale with different configurations
docker-compose -f docker-compose.yml -f docker-compose.scale.yml up -d
```

### Vertical Scaling
```yaml
# docker-compose.override.yml
version: '3.8'
services:
  analysis-worker:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

## Monitoring

### Health Checks
```bash
# Check all services
curl http://localhost:3000/health
curl http://localhost:3001/health
curl http://localhost:3002/health
curl http://localhost:3003/health
curl http://localhost:3005/health

# Using script
npm run health
```

### Logging
```bash
# View logs
docker-compose logs -f

# Service-specific logs
docker-compose logs -f auth-service
docker-compose logs -f analysis-worker

# Log files (if using file logging)
tail -f auth-service/logs/auth-service.log
tail -f analysis-worker/logs/analysis-worker.log
```

### Metrics Collection
```yaml
# Add to docker-compose.yml for Prometheus monitoring
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## Security

### Production Security Checklist
- [ ] Change default passwords
- [ ] Use strong JWT secrets (minimum 32 characters)
- [ ] Enable HTTPS/TLS
- [ ] Configure firewall rules
- [ ] Set up VPN for internal communication
- [ ] Enable audit logging
- [ ] Regular security updates
- [ ] Backup encryption

### Network Security
```bash
# Firewall rules (example for Ubuntu)
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5432/tcp   # PostgreSQL (internal only)
ufw deny 5672/tcp   # RabbitMQ (internal only)
ufw enable
```

## Backup & Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U postgres -n auth atma_db > backup_auth_$DATE.sql
pg_dump -h localhost -U postgres -n archive atma_db > backup_archive_$DATE.sql

# Full database backup
pg_dump -h localhost -U postgres atma_db > backup_full_$DATE.sql

# Compress backups
gzip backup_*_$DATE.sql

# Upload to cloud storage (example)
aws s3 cp backup_auth_$DATE.sql.gz s3://your-backup-bucket/
aws s3 cp backup_archive_$DATE.sql.gz s3://your-backup-bucket/
aws s3 cp backup_full_$DATE.sql.gz s3://your-backup-bucket/
```

### Recovery
```bash
# Restore specific schema
psql -h localhost -U postgres -d atma_db < backup_auth_20240101_120000.sql
psql -h localhost -U postgres -d atma_db < backup_archive_20240101_120000.sql

# Restore full database
psql -h localhost -U postgres -d atma_db < backup_full_20240101_120000.sql
```

## Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose logs service-name

# Check environment variables
docker-compose exec service-name env

# Check network connectivity
docker-compose exec service-name ping postgres
```

#### Database Connection Issues
```bash
# Test connection
docker-compose exec auth-service npm run test:db

# Check PostgreSQL logs
docker-compose logs postgres
```

#### RabbitMQ Issues
```bash
# Check queue status
docker-compose exec rabbitmq rabbitmqctl list_queues

# Reset RabbitMQ
docker-compose restart rabbitmq
```

#### High Memory Usage
```bash
# Check memory usage
docker stats

# Restart services
docker-compose restart

# Scale down workers
docker-compose up -d --scale analysis-worker=1
```

### Performance Optimization

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX CONCURRENTLY idx_users_email_hash ON users USING hash(email);
CREATE INDEX CONCURRENTLY idx_analysis_results_user_created ON analysis_results(user_id, created_at);

-- Analyze tables
ANALYZE users;
ANALYZE analysis_results;
```

#### Worker Optimization
```env
# Adjust worker settings
WORKER_CONCURRENCY=2
PROCESSING_TIMEOUT=180000
MAX_RETRIES=2
```

## Maintenance

### Regular Maintenance Tasks
```bash
# Weekly tasks
docker system prune -f
npm audit fix
docker-compose pull

# Monthly tasks
pg_dump databases
rotate logs
update dependencies
security scan
```

### Updates
```bash
# Update services
git pull origin main
npm run install:all
docker-compose build
docker-compose up -d

# Rolling update (zero downtime)
docker-compose up -d --no-deps service-name
```
