#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting ATMA API Gateway in development mode...');

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.PORT = process.env.PORT || '3000';

// Check if nodemon is installed locally
const nodeModulesPath = path.join(__dirname, '..', 'node_modules', '.bin');
const nodemonPath = path.join(nodeModulesPath, process.platform === 'win32' ? 'nodemon.cmd' : 'nodemon');

if (!fs.existsSync(nodemonPath)) {
  console.error('❌ Nodemon not found. Please run "npm install" first.');
  process.exit(1);
}

// Start the application with nodemon
const nodemon = spawn(nodemonPath, ['src/app.js'], {
  cwd: path.join(__dirname, '..'),
  stdio: 'inherit',
  env: process.env
});

nodemon.on('close', (code) => {
  console.log(`\n📦 API Gateway process exited with code ${code}`);
  process.exit(code);
});

nodemon.on('error', (err) => {
  console.error('❌ Failed to start API Gateway:', err);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down API Gateway...');
  nodemon.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down API Gateway...');
  nodemon.kill('SIGTERM');
});
