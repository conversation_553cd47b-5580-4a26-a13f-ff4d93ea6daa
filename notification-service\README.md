# Notification Service

Service untuk mengirim notifikasi real-time ke frontend menggunakan WebSocket ketika analisis assessment selesai diproses.

## Fungsi Utama

1. **WebSocket Server**: Menyediakan koneksi WebSocket untuk real-time communication
2. **Event Listener**: Mendengarkan event dari Analysis Worker
3. **User Notification**: Mengirim notifikasi ke user yang tepat
4. **Connection Management**: Mengelola koneksi WebSocket per user
5. **Authentication**: Verifikasi JWT token untuk WebSocket connections

## Port
- **Development**: 3005
- **Production**: 3005

## Dependencies

```json
{
  "express": "^4.18.2",
  "socket.io": "^4.6.1",
  "amqplib": "^0.10.3",
  "jsonwebtoken": "^9.0.0",
  "dotenv": "^16.0.3",
  "cors": "^2.8.5",
  "winston": "^3.8.2"
}
```

## Environment Variables

```env
PORT=3005
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_jwt_secret_key

# RabbitMQ Configuration (for listening to events)
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
NOTIFICATION_QUEUE=analysis_notifications
EXCHANGE_NAME=atma_exchange

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# WebSocket Configuration
WEBSOCKET_CORS_ORIGIN=http://localhost:3000,http://localhost:3001
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/notification-service.log
```

## WebSocket Integration

### Client Connection
Frontend connects to WebSocket dengan JWT authentication:

```javascript
// Frontend JavaScript
import io from 'socket.io-client';

const socket = io('http://localhost:3005', {
  auth: {
    token: 'jwt_token_here'
  }
});

// Listen for analysis completion
socket.on('analysis-complete', (data) => {
  console.log('Analysis completed:', data);
  // Update UI, show notification, redirect to results, etc.
});

// Listen for analysis progress (optional)
socket.on('analysis-progress', (data) => {
  console.log('Analysis progress:', data);
  // Update progress bar
});

// Handle connection events
socket.on('connect', () => {
  console.log('Connected to notification service');
});

socket.on('disconnect', () => {
  console.log('Disconnected from notification service');
});
```

### Server Events

#### analysis-complete
Dikirim ketika analisis selesai dan hasil tersimpan di Archive Service.

**Event Data:**
```json
{
  "type": "analysis-complete",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "resultId": "550e8400-e29b-41d4-a716-446655440002",
  "status": "completed",
  "timestamp": "2024-01-01T00:05:00.000Z",
  "message": "Your personality analysis is ready!",
  "data": {
    "archetype": "The Innovator",
    "processingTime": "3.2 minutes"
  }
}
```

#### analysis-failed
Dikirim ketika analisis gagal.

**Event Data:**
```json
{
  "type": "analysis-failed",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "failed",
  "timestamp": "2024-01-01T00:05:00.000Z",
  "message": "Analysis failed. Please try again.",
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "Temporary service unavailable"
  }
}
```

#### analysis-progress (Optional)
Dikirim untuk update progress analisis.

**Event Data:**
```json
{
  "type": "analysis-progress",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "progress": 75,
  "stage": "Generating insights",
  "timestamp": "2024-01-01T00:03:00.000Z"
}
```

## HTTP API Endpoints

### POST /notifications/analysis-complete
Endpoint untuk Analysis Worker mengirim notifikasi completion (internal only)

**Headers:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key
Content-Type: application/json
```

**Request Body:**
```json
{
  "userId": "550e8400-e29b-41d4-a716-446655440001",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "resultId": "550e8400-e29b-41d4-a716-446655440002",
  "status": "completed",
  "data": {
    "archetype": "The Innovator",
    "processingTime": "3.2 minutes"
  }
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "userId": "550e8400-e29b-41d4-a716-446655440001",
    "delivered": true,
    "connectedClients": 1
  }
}
```

### POST /notifications/analysis-failed
Endpoint untuk mengirim notifikasi failure (internal only)

**Headers:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key
Content-Type: application/json
```

**Request Body:**
```json
{
  "userId": "550e8400-e29b-41d4-a716-446655440001",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "failed",
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "Temporary service unavailable"
  }
}
```

### GET /notifications/status
Mendapatkan status koneksi WebSocket user

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "connectionId": "socket_connection_id",
    "connectedAt": "2024-01-01T00:00:00.000Z",
    "lastActivity": "2024-01-01T00:02:00.000Z"
  }
}
```

### POST /notifications/test
Test endpoint untuk mengirim notifikasi test (development only)

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Request Body:**
```json
{
  "type": "test",
  "message": "This is a test notification"
}
```

## Health Check

### GET /health
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "websocket": "running",
  "connectedClients": 5,
  "rabbitmq": "connected",
  "version": "1.0.0"
}
```

## Connection Management

### User Authentication
WebSocket connections diautentikasi menggunakan JWT token:

```javascript
// Server-side authentication
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.id;
    socket.userEmail = decoded.email;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});
```

### Connection Tracking
Server melacak koneksi per user untuk mengirim notifikasi yang tepat:

```javascript
// Connection mapping
const userConnections = new Map();

io.on('connection', (socket) => {
  // Store user connection
  userConnections.set(socket.userId, socket);
  
  socket.on('disconnect', () => {
    // Remove user connection
    userConnections.delete(socket.userId);
  });
});
```

### Message Broadcasting
Notifikasi dikirim hanya ke user yang tepat:

```javascript
// Send notification to specific user
function sendNotificationToUser(userId, eventType, data) {
  const userSocket = userConnections.get(userId);
  if (userSocket) {
    userSocket.emit(eventType, data);
    return true;
  }
  return false;
}
```

## Error Handling

### WebSocket Errors
- **Authentication Failed**: Disconnect dengan error message
- **Connection Lost**: Auto-reconnect di client side
- **Invalid Message**: Log error, tidak disconnect

### Message Queue Errors
- **Connection Lost**: Auto-reconnect dengan exponential backoff
- **Message Processing Error**: Log error, acknowledge message
- **Invalid Message Format**: Send to dead letter queue

## Security Features

1. **JWT Authentication**: Semua WebSocket connections harus authenticated
2. **CORS Configuration**: Whitelist allowed origins
3. **Internal Service Protection**: Endpoint internal dilindungi dengan service key
4. **Rate Limiting**: Prevent spam connections (implementasi di API Gateway)
5. **Input Validation**: Validasi semua incoming messages

## Monitoring & Logging

### Log Events
- WebSocket connection established
- WebSocket disconnection
- Notification sent successfully
- Notification delivery failed
- Message queue events
- Authentication failures

### Metrics
- Active WebSocket connections
- Notifications sent per minute
- Delivery success rate
- Average connection duration

## Development & Testing

### Local Development
```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env

# Start service
npm run dev
```

### Testing WebSocket
```javascript
// Test client
const io = require('socket.io-client');
const socket = io('http://localhost:3005', {
  auth: { token: 'your_jwt_token' }
});

socket.on('connect', () => {
  console.log('Connected');
});

socket.on('analysis-complete', (data) => {
  console.log('Received notification:', data);
});
```
