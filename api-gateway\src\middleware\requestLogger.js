const logger = require('../utils/logger');

/**
 * Middleware untuk logging request dan response yang detail
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Log incoming request
  const requestInfo = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    userId: req.user?.id,
    userEmail: req.user?.email,
    timestamp: new Date().toISOString()
  };

  logger.info('Incoming request', requestInfo);

  // Capture original res.end to log response
  const originalEnd = res.end;
  const originalSend = res.send;
  
  let responseBody = '';
  
  // Override res.send to capture response body
  res.send = function(body) {
    responseBody = body;
    return originalSend.call(this, body);
  };

  // Override res.end to log response
  res.end = function(chunk, encoding) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const responseInfo = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length'),
      userId: req.user?.id,
      userEmail: req.user?.email,
      ip: req.ip,
      timestamp: new Date().toISOString()
    };

    // Log based on status code
    if (res.statusCode >= 400) {
      logger.error('Request completed with error', {
        ...responseInfo,
        error: res.statusCode >= 500 ? 'Server Error' : 'Client Error'
      });
    } else {
      logger.info('Request completed successfully', responseInfo);
    }

    // Call original end
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Middleware untuk logging error requests secara khusus
 */
const errorLogger = (err, req, res, next) => {
  const errorInfo = {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      userEmail: req.user?.email
    },
    timestamp: new Date().toISOString()
  };

  logger.error('Request error occurred', errorInfo);
  next(err);
};

/**
 * Middleware untuk logging security events
 */
const securityLogger = (event, req, details = {}) => {
  const securityInfo = {
    event,
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      userEmail: req.user?.email
    },
    details,
    timestamp: new Date().toISOString()
  };

  logger.warn('Security event', securityInfo);
};

module.exports = {
  requestLogger,
  errorLogger,
  securityLogger
};
