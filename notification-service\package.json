{"name": "atma-notification-service", "version": "1.0.0", "description": "Notification Service for ATMA Backend - Real-time WebSocket notifications", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.6.1", "amqplib": "^0.10.3", "jsonwebtoken": "^9.0.0", "dotenv": "^16.0.3", "cors": "^2.8.5", "winston": "^3.8.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.39.0", "socket.io-client": "^4.6.1"}, "keywords": ["notification", "websocket", "socket.io", "real-time", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}